#!/usr/bin/env python3
"""
Results Comparison Script for Vidur vs vLLM

This script compares the results from Vidur simulation and real vLLM engine,
calculates errors, and generates analysis reports.
"""

import argparse
import json
import os
import glob
from typing import Dict, Any, Optional, List, Tuple
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from dataclasses import dataclass


@dataclass
class ComparisonResult:
    """Results of comparison between Vidur and vLLM"""
    metric_name: str
    vllm_value: float
    vidur_value: float
    absolute_error: float
    relative_error_percent: float
    
    
class ResultsComparator:
    """Compare results between Vidur and vLLM"""
    
    def __init__(self, output_dir: str = "./comparison_output"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        
    def load_vllm_results(self, results_dir: str) -> Dict[str, Any]:
        """Load vLLM benchmark results"""
        # Find the latest vLLM summary file
        summary_files = glob.glob(os.path.join(results_dir, "vllm_summary_*.json"))
        if not summary_files:
            raise FileNotFoundError(f"No vLLM summary files found in {results_dir}")
            
        latest_file = max(summary_files, key=os.path.getctime)
        print(f"Loading vLLM results from: {latest_file}")
        
        with open(latest_file, 'r') as f:
            return json.load(f)
            
    def load_vidur_results(self, results_dir: str) -> Dict[str, Any]:
        """Load Vidur simulation results"""
        # Find the latest Vidur summary file
        summary_files = glob.glob(os.path.join(results_dir, "vidur_summary_*.json"))
        if not summary_files:
            raise FileNotFoundError(f"No Vidur summary files found in {results_dir}")
            
        latest_file = max(summary_files, key=os.path.getctime)
        print(f"Loading Vidur results from: {latest_file}")
        
        with open(latest_file, 'r') as f:
            return json.load(f)
            
    def calculate_error(self, vllm_value: float, vidur_value: float) -> Tuple[float, float]:
        """Calculate absolute and relative error"""
        if vllm_value is None or vidur_value is None:
            return float('nan'), float('nan')
            
        absolute_error = abs(vidur_value - vllm_value)
        relative_error_percent = (absolute_error / vllm_value) * 100 if vllm_value != 0 else float('inf')
        
        return absolute_error, relative_error_percent
        
    def compare_metrics(self, vllm_results: Dict[str, Any], vidur_results: Dict[str, Any]) -> List[ComparisonResult]:
        """Compare key metrics between vLLM and Vidur"""
        comparisons = []
        
        # Define metrics to compare
        metrics_to_compare = [
            ('throughput_qps', 'Throughput (QPS)'),
            ('ttft_stats.mean', 'TTFT Mean (s)'),
            ('ttft_stats.median', 'TTFT Median (s)'),
            ('ttft_stats.p95', 'TTFT P95 (s)'),
            ('ttft_stats.p99', 'TTFT P99 (s)'),
            ('tpot_stats.mean', 'TPOT Mean (s)'),
            ('tpot_stats.median', 'TPOT Median (s)'),
            ('tpot_stats.p95', 'TPOT P95 (s)'),
            ('tpot_stats.p99', 'TPOT P99 (s)'),
            ('e2e_latency_stats.mean', 'E2E Latency Mean (s)'),
            ('e2e_latency_stats.median', 'E2E Latency Median (s)'),
            ('e2e_latency_stats.p95', 'E2E Latency P95 (s)'),
            ('e2e_latency_stats.p99', 'E2E Latency P99 (s)'),
        ]
        
        for metric_key, metric_name in metrics_to_compare:
            vllm_value = self._get_nested_value(vllm_results, metric_key)
            vidur_value = self._get_nested_value(vidur_results, metric_key)
            
            if vllm_value is not None and vidur_value is not None:
                abs_error, rel_error = self.calculate_error(vllm_value, vidur_value)
                
                comparison = ComparisonResult(
                    metric_name=metric_name,
                    vllm_value=vllm_value,
                    vidur_value=vidur_value,
                    absolute_error=abs_error,
                    relative_error_percent=rel_error
                )
                comparisons.append(comparison)
            else:
                print(f"Warning: Missing data for {metric_name} (vLLM: {vllm_value}, Vidur: {vidur_value})")
                
        return comparisons
        
    def _get_nested_value(self, data: Dict[str, Any], key: str) -> Optional[float]:
        """Get nested value from dictionary using dot notation"""
        keys = key.split('.')
        value = data
        
        try:
            for k in keys:
                value = value[k]
            return float(value) if value is not None else None
        except (KeyError, TypeError, ValueError):
            return None
            
    def generate_comparison_report(self, comparisons: List[ComparisonResult], 
                                 vllm_results: Dict[str, Any], vidur_results: Dict[str, Any]) -> None:
        """Generate detailed comparison report"""
        
        # Create comparison DataFrame
        comparison_data = []
        for comp in comparisons:
            comparison_data.append({
                'Metric': comp.metric_name,
                'vLLM': comp.vllm_value,
                'Vidur': comp.vidur_value,
                'Absolute Error': comp.absolute_error,
                'Relative Error (%)': comp.relative_error_percent
            })
            
        df = pd.DataFrame(comparison_data)
        
        # Save detailed comparison
        comparison_file = os.path.join(self.output_dir, "detailed_comparison.csv")
        df.to_csv(comparison_file, index=False)
        print(f"Detailed comparison saved to: {comparison_file}")
        
        # Calculate overall statistics
        valid_errors = [comp.relative_error_percent for comp in comparisons 
                       if not np.isnan(comp.relative_error_percent) and not np.isinf(comp.relative_error_percent)]
        
        if valid_errors:
            mean_error = np.mean(valid_errors)
            median_error = np.median(valid_errors)
            max_error = np.max(valid_errors)
            min_error = np.min(valid_errors)
            
            # Check if within 9% error threshold
            errors_within_9_percent = [e for e in valid_errors if e <= 9.0]
            percentage_within_9 = (len(errors_within_9_percent) / len(valid_errors)) * 100
            
            summary = {
                'overall_statistics': {
                    'mean_relative_error_percent': mean_error,
                    'median_relative_error_percent': median_error,
                    'max_relative_error_percent': max_error,
                    'min_relative_error_percent': min_error,
                    'percentage_within_9_percent': percentage_within_9,
                    'total_metrics_compared': len(valid_errors)
                },
                'vllm_config': vllm_results.get('config', {}),
                'vidur_config': vidur_results.get('config', {}),
                'detailed_comparisons': comparison_data
            }
            
            # Save summary
            summary_file = os.path.join(self.output_dir, "comparison_summary.json")
            with open(summary_file, 'w') as f:
                json.dump(summary, f, indent=2)
            print(f"Comparison summary saved to: {summary_file}")
            
            # Print results
            print("\n" + "="*60)
            print("VIDUR vs vLLM COMPARISON RESULTS")
            print("="*60)
            print(f"Total metrics compared: {len(valid_errors)}")
            print(f"Mean relative error: {mean_error:.2f}%")
            print(f"Median relative error: {median_error:.2f}%")
            print(f"Max relative error: {max_error:.2f}%")
            print(f"Min relative error: {min_error:.2f}%")
            print(f"Metrics within 9% error: {len(errors_within_9_percent)}/{len(valid_errors)} ({percentage_within_9:.1f}%)")
            
            if mean_error <= 9.0:
                print("\n✅ SUCCESS: Mean error is within 9% threshold!")
            else:
                print(f"\n❌ FAILURE: Mean error ({mean_error:.2f}%) exceeds 9% threshold")
                
            print("\nDetailed Metrics Comparison:")
            print("-" * 80)
            for comp in comparisons:
                if not np.isnan(comp.relative_error_percent) and not np.isinf(comp.relative_error_percent):
                    status = "✅" if comp.relative_error_percent <= 9.0 else "❌"
                    print(f"{status} {comp.metric_name:25} | vLLM: {comp.vllm_value:8.4f} | Vidur: {comp.vidur_value:8.4f} | Error: {comp.relative_error_percent:6.2f}%")
                    
        else:
            print("No valid comparisons could be made")
            
    def create_visualization(self, comparisons: List[ComparisonResult]) -> None:
        """Create visualization of comparison results"""
        if not comparisons:
            print("No data to visualize")
            return
            
        # Filter valid comparisons
        valid_comparisons = [comp for comp in comparisons 
                           if not np.isnan(comp.relative_error_percent) and not np.isinf(comp.relative_error_percent)]
        
        if not valid_comparisons:
            print("No valid data to visualize")
            return
            
        # Create plots
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # Plot 1: Relative Error by Metric
        metrics = [comp.metric_name for comp in valid_comparisons]
        errors = [comp.relative_error_percent for comp in valid_comparisons]
        
        ax1.barh(metrics, errors)
        ax1.axvline(x=9.0, color='red', linestyle='--', label='9% Threshold')
        ax1.set_xlabel('Relative Error (%)')
        ax1.set_title('Relative Error by Metric')
        ax1.legend()
        
        # Plot 2: vLLM vs Vidur Values
        vllm_values = [comp.vllm_value for comp in valid_comparisons]
        vidur_values = [comp.vidur_value for comp in valid_comparisons]
        
        ax2.scatter(vllm_values, vidur_values, alpha=0.7)
        min_val = min(min(vllm_values), min(vidur_values))
        max_val = max(max(vllm_values), max(vidur_values))
        ax2.plot([min_val, max_val], [min_val, max_val], 'r--', label='Perfect Agreement')
        ax2.set_xlabel('vLLM Values')
        ax2.set_ylabel('Vidur Values')
        ax2.set_title('vLLM vs Vidur Values')
        ax2.legend()
        
        # Plot 3: Error Distribution
        ax3.hist(errors, bins=10, alpha=0.7, edgecolor='black')
        ax3.axvline(x=9.0, color='red', linestyle='--', label='9% Threshold')
        ax3.set_xlabel('Relative Error (%)')
        ax3.set_ylabel('Frequency')
        ax3.set_title('Distribution of Relative Errors')
        ax3.legend()
        
        # Plot 4: Error vs Metric Type
        metric_types = []
        for comp in valid_comparisons:
            if 'TTFT' in comp.metric_name:
                metric_types.append('TTFT')
            elif 'TPOT' in comp.metric_name:
                metric_types.append('TPOT')
            elif 'E2E' in comp.metric_name:
                metric_types.append('E2E Latency')
            elif 'Throughput' in comp.metric_name:
                metric_types.append('Throughput')
            else:
                metric_types.append('Other')
                
        df_plot = pd.DataFrame({'Metric Type': metric_types, 'Relative Error (%)': errors})
        sns.boxplot(data=df_plot, x='Metric Type', y='Relative Error (%)', ax=ax4)
        ax4.axhline(y=9.0, color='red', linestyle='--', label='9% Threshold')
        ax4.set_title('Error Distribution by Metric Type')
        ax4.legend()
        
        plt.tight_layout()
        
        # Save plot
        plot_file = os.path.join(self.output_dir, "comparison_plots.png")
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        print(f"Visualization saved to: {plot_file}")
        
        plt.show()


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Compare Vidur and vLLM benchmark results")

    parser.add_argument("--vllm-results-dir", type=str, default="./benchmark_output",
                        help="Directory containing vLLM results (default: ./benchmark_output)")
    parser.add_argument("--vidur-results-dir", type=str, default="./benchmark_output",
                        help="Directory containing Vidur results (default: ./benchmark_output)")
    parser.add_argument("--output-dir", type=str, default="./comparison_output",
                        help="Output directory for comparison results (default: ./comparison_output)")
    parser.add_argument("--no-plots", action="store_true",
                        help="Skip generating plots")

    args = parser.parse_args()

    print("Results Comparison Configuration:")
    print(f"  vLLM Results Directory: {args.vllm_results_dir}")
    print(f"  Vidur Results Directory: {args.vidur_results_dir}")
    print(f"  Output Directory: {args.output_dir}")
    print(f"  Generate Plots: {not args.no_plots}")
    print()

    # Create comparator
    comparator = ResultsComparator(args.output_dir)

    try:
        # Load results
        print("Loading benchmark results...")
        vllm_results = comparator.load_vllm_results(args.vllm_results_dir)
        vidur_results = comparator.load_vidur_results(args.vidur_results_dir)

        # Compare metrics
        print("Comparing metrics...")
        comparisons = comparator.compare_metrics(vllm_results, vidur_results)

        # Generate report
        print("Generating comparison report...")
        comparator.generate_comparison_report(comparisons, vllm_results, vidur_results)

        # Create visualization
        if not args.no_plots:
            print("Creating visualizations...")
            comparator.create_visualization(comparisons)

        print("\nComparison completed successfully!")

    except Exception as e:
        print(f"Comparison failed: {e}")
        raise


if __name__ == "__main__":
    main()
