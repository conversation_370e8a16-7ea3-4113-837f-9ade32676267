#!/usr/bin/env python3
"""
Simple test to verify Vidur configuration works
"""

import sys
import os

# Add vidur to path
sys.path.insert(0, '.')

from benchmark_vidur import VidurBenchmarkConfig, VidurBenchmark

def test_vidur_config():
    """Test Vidur configuration creation"""
    print("Testing Vidur configuration...")
    
    # Create minimal config
    config = VidurBenchmarkConfig(
        model_name="meta-llama/Meta-Llama-3-8B",
        device="a100",
        tensor_parallel_size=1,
        num_pipeline_stages=1,
        max_model_len=1024,
        block_size=16,
        batch_size_cap=32,
        max_tokens_in_batch=512,
        trace_file="./data/processed_traces/splitwise_conv.csv",
        output_dir="./test_output",
        num_requests=3,
        qps=None,
        seed=42,
    )
    
    print(f"Config created: {config}")
    
    # Create benchmark instance
    benchmark = VidurBenchmark(config)
    
    try:
        # Test config creation (this was failing before)
        sim_config = benchmark.create_vidur_config()
        print("✅ Vidur configuration created successfully!")
        print(f"Model: {sim_config.cluster_config.replica_config.model_name}")
        print(f"Device: {sim_config.cluster_config.replica_config.device}")
        print(f"Scheduler: {sim_config.cluster_config.replica_config.replica_scheduler_config.get_type()}")
        return True
        
    except Exception as e:
        print(f"❌ Vidur configuration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_vidur_config()
    sys.exit(0 if success else 1)
