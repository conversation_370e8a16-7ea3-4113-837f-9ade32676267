#!/usr/bin/env python3
"""
vLLM Benchmark Script for Vidur Comparison

This script runs real vLLM engine with specified configurations and collects
performance metrics for comparison with Vidur simulator.
"""

import argparse
import asyncio
import csv
import json
import os
import time
from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Optional
import pandas as pd
import numpy as np

try:
    from vllm import LLM, SamplingParams
    from vllm.engine.arg_utils import AsyncEngineArgs
    from vllm.engine.async_llm_engine import AsyncLLMEngine
except ImportError:
    print("vLLM not installed. Please install vLLM to run this benchmark.")
    print("pip install vllm")
    exit(1)


@dataclass
class RequestMetrics:
    """Metrics for a single request"""
    request_id: str
    arrived_at: float
    num_prefill_tokens: int
    num_decode_tokens: int
    start_time: float
    first_token_time: Optional[float]
    end_time: float
    ttft: Optional[float]  # Time to First Token
    tpot: Optional[float]  # Time per Output Token
    e2e_latency: float  # End-to-end latency
    actual_output_tokens: int


@dataclass
class BenchmarkConfig:
    """Configuration for the benchmark"""
    model_name: str = "/share_data/llm_weights/Meta-Llama-3-8B"
    device: str = "cuda"
    tensor_parallel_size: int = 1
    max_model_len: int = 4096
    block_size: int = 16
    max_num_seqs: int = 64
    trace_file: str = "./data/processed_traces/splitwise_conv.csv"
    output_dir: str = "./benchmark_output"
    num_requests: Optional[int] = None  # If None, use all requests from trace
    qps: Optional[float] = None  # If None, use arrival times from trace


class VLLMBenchmark:
    """vLLM benchmark runner"""
    
    def __init__(self, config: BenchmarkConfig):
        self.config = config
        self.requests: List[Dict[str, Any]] = []
        self.results: List[RequestMetrics] = []
        self.engine: Optional[AsyncLLMEngine] = None
        
        # Create output directory
        os.makedirs(config.output_dir, exist_ok=True)
        
    def load_trace(self) -> None:
        """Load request trace from CSV file"""
        print(f"Loading trace from {self.config.trace_file}")
        
        df = pd.read_csv(self.config.trace_file)
        
        if self.config.num_requests:
            df = df.head(self.config.num_requests)
            
        self.requests = []
        for idx, row in df.iterrows():
            request = {
                'request_id': f"req_{idx}",
                'arrived_at': float(row['arrived_at']),
                'num_prefill_tokens': int(row['num_prefill_tokens']),
                'num_decode_tokens': int(row['num_decode_tokens']),
            }
            self.requests.append(request)
            
        print(f"Loaded {len(self.requests)} requests")
        
    async def initialize_engine(self) -> None:
        """Initialize the vLLM async engine"""
        print("Initializing vLLM engine...")
        
        engine_args = AsyncEngineArgs(
            model=self.config.model_name,
            tensor_parallel_size=self.config.tensor_parallel_size,
            max_model_len=self.config.max_model_len,
            block_size=self.config.block_size,
            max_num_seqs=self.config.max_num_seqs,
            trust_remote_code=True,
        )
        
        self.engine = AsyncLLMEngine.from_engine_args(engine_args)
        print("vLLM engine initialized")
        
    async def run_single_request(self, request: Dict[str, Any]) -> RequestMetrics:
        """Run a single request and collect metrics"""
        request_id = request['request_id']
        num_prefill_tokens = request['num_prefill_tokens']
        num_decode_tokens = request['num_decode_tokens']
        
        # Create a dummy prompt with approximately the right number of tokens
        # Using "hello " repeated to approximate token count
        prompt = "hello " * (num_prefill_tokens // 2)
        
        sampling_params = SamplingParams(
            max_tokens=num_decode_tokens,
            temperature=0.0,  # Deterministic for reproducibility
        )
        
        start_time = time.time()
        first_token_time = None
        actual_output_tokens = 0
        
        # Generate response
        async for output in self.engine.generate(prompt, sampling_params, request_id):
            if first_token_time is None and len(output.outputs[0].token_ids) > 0:
                first_token_time = time.time()
            actual_output_tokens = len(output.outputs[0].token_ids)
            
        end_time = time.time()
        
        # Calculate metrics
        ttft = first_token_time - start_time if first_token_time else None
        e2e_latency = end_time - start_time
        tpot = None
        if first_token_time and actual_output_tokens > 1:
            decode_time = end_time - first_token_time
            tpot = decode_time / (actual_output_tokens - 1)
            
        return RequestMetrics(
            request_id=request_id,
            arrived_at=request['arrived_at'],
            num_prefill_tokens=num_prefill_tokens,
            num_decode_tokens=num_decode_tokens,
            start_time=start_time,
            first_token_time=first_token_time,
            end_time=end_time,
            ttft=ttft,
            tpot=tpot,
            e2e_latency=e2e_latency,
            actual_output_tokens=actual_output_tokens,
        )

    async def run_benchmark(self) -> None:
        """Run the complete benchmark"""
        print("Starting vLLM benchmark...")

        # Load trace and initialize engine
        self.load_trace()
        await self.initialize_engine()

        # Calculate request timing
        if self.config.qps:
            # Use fixed QPS
            interval = 1.0 / self.config.qps
            request_times = [i * interval for i in range(len(self.requests))]
        else:
            # Use arrival times from trace
            request_times = [req['arrived_at'] for req in self.requests]

        # Normalize to start from 0
        min_time = min(request_times)
        request_times = [t - min_time for t in request_times]

        # Run requests
        benchmark_start = time.time()
        tasks = []

        for i, (request, arrival_time) in enumerate(zip(self.requests, request_times)):
            # Wait until it's time to send this request
            current_time = time.time() - benchmark_start
            if arrival_time > current_time:
                await asyncio.sleep(arrival_time - current_time)

            # Start the request
            task = asyncio.create_task(self.run_single_request(request))
            tasks.append(task)

            if (i + 1) % 100 == 0:
                print(f"Submitted {i + 1}/{len(self.requests)} requests")

        # Wait for all requests to complete
        print("Waiting for all requests to complete...")
        self.results = await asyncio.gather(*tasks)

        print(f"Benchmark completed. Processed {len(self.results)} requests")

    def save_results(self) -> None:
        """Save benchmark results to files"""
        timestamp = int(time.time())

        # Save detailed metrics
        results_file = os.path.join(self.config.output_dir, f"vllm_results_{timestamp}.csv")
        with open(results_file, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow([
                'request_id', 'arrived_at', 'num_prefill_tokens', 'num_decode_tokens',
                'start_time', 'first_token_time', 'end_time', 'ttft', 'tpot',
                'e2e_latency', 'actual_output_tokens'
            ])

            for result in self.results:
                writer.writerow([
                    result.request_id, result.arrived_at, result.num_prefill_tokens,
                    result.num_decode_tokens, result.start_time, result.first_token_time,
                    result.end_time, result.ttft, result.tpot, result.e2e_latency,
                    result.actual_output_tokens
                ])

        print(f"Detailed results saved to {results_file}")

        # Calculate and save summary statistics
        self.calculate_and_save_summary(timestamp)

    def calculate_and_save_summary(self, timestamp: int) -> None:
        """Calculate and save summary statistics"""
        # Filter out None values for calculations
        ttfts = [r.ttft for r in self.results if r.ttft is not None]
        tpots = [r.tpot for r in self.results if r.tpot is not None]
        e2e_latencies = [r.e2e_latency for r in self.results]

        # Calculate statistics
        summary = {
            'config': asdict(self.config),
            'total_requests': len(self.results),
            'successful_requests': len([r for r in self.results if r.ttft is not None]),
            'benchmark_duration': max(r.end_time for r in self.results) - min(r.start_time for r in self.results),
            'throughput_qps': len(self.results) / (max(r.end_time for r in self.results) - min(r.start_time for r in self.results)),
            'ttft_stats': {
                'mean': np.mean(ttfts) if ttfts else None,
                'median': np.median(ttfts) if ttfts else None,
                'p95': np.percentile(ttfts, 95) if ttfts else None,
                'p99': np.percentile(ttfts, 99) if ttfts else None,
                'std': np.std(ttfts) if ttfts else None,
            },
            'tpot_stats': {
                'mean': np.mean(tpots) if tpots else None,
                'median': np.median(tpots) if tpots else None,
                'p95': np.percentile(tpots, 95) if tpots else None,
                'p99': np.percentile(tpots, 99) if tpots else None,
                'std': np.std(tpots) if tpots else None,
            },
            'e2e_latency_stats': {
                'mean': np.mean(e2e_latencies),
                'median': np.median(e2e_latencies),
                'p95': np.percentile(e2e_latencies, 95),
                'p99': np.percentile(e2e_latencies, 99),
                'std': np.std(e2e_latencies),
            }
        }

        # Save summary
        summary_file = os.path.join(self.config.output_dir, f"vllm_summary_{timestamp}.json")
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)

        print(f"Summary statistics saved to {summary_file}")

        # Print key metrics
        print("\n=== vLLM Benchmark Results ===")
        print(f"Total requests: {summary['total_requests']}")
        print(f"Successful requests: {summary['successful_requests']}")
        print(f"Throughput: {summary['throughput_qps']:.2f} QPS")
        if summary['ttft_stats']['mean']:
            print(f"TTFT - Mean: {summary['ttft_stats']['mean']:.3f}s, P95: {summary['ttft_stats']['p95']:.3f}s")
        if summary['tpot_stats']['mean']:
            print(f"TPOT - Mean: {summary['tpot_stats']['mean']:.3f}s, P95: {summary['tpot_stats']['p95']:.3f}s")
        print(f"E2E Latency - Mean: {summary['e2e_latency_stats']['mean']:.3f}s, P95: {summary['e2e_latency_stats']['p95']:.3f}s")


async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="vLLM Benchmark for Vidur Comparison")

    # Model and hardware configuration
    parser.add_argument("--model-name", type=str, default="/share_data/llm_weights/Meta-Llama-3-8B",
                        help="Model name (default: /share_data/llm_weights/Meta-Llama-3-8B)")
    parser.add_argument("--tensor-parallel-size", type=int, default=1,
                        help="Tensor parallel size (default: 1)")
    parser.add_argument("--max-model-len", type=int, default=4096,
                        help="Maximum model length (default: 4096)")
    parser.add_argument("--block-size", type=int, default=16,
                        help="Block size (default: 16)")
    parser.add_argument("--max-num-seqs", type=int, default=64,
                        help="Maximum number of sequences (default: 64)")

    # Workload configuration
    parser.add_argument("--trace-file", type=str, default="./data/processed_traces/splitwise_conv.csv",
                        help="Path to trace file (default: ./data/processed_traces/splitwise_conv.csv)")
    parser.add_argument("--num-requests", type=int, default=None,
                        help="Number of requests to process (default: all)")
    parser.add_argument("--qps", type=float, default=None,
                        help="Queries per second (default: use trace timing)")

    # Output configuration
    parser.add_argument("--output-dir", type=str, default="./benchmark_output",
                        help="Output directory (default: ./benchmark_output)")

    args = parser.parse_args()

    # Create configuration
    config = BenchmarkConfig(
        model_name=args.model_name,
        tensor_parallel_size=args.tensor_parallel_size,
        max_model_len=args.max_model_len,
        block_size=args.block_size,
        max_num_seqs=args.max_num_seqs,
        trace_file=args.trace_file,
        output_dir=args.output_dir,
        num_requests=args.num_requests,
        qps=args.qps,
    )

    print("vLLM Benchmark Configuration:")
    print(f"  Model: {config.model_name}")
    print(f"  Tensor Parallel Size: {config.tensor_parallel_size}")
    print(f"  Max Model Length: {config.max_model_len}")
    print(f"  Block Size: {config.block_size}")
    print(f"  Max Sequences: {config.max_num_seqs}")
    print(f"  Trace File: {config.trace_file}")
    print(f"  Number of Requests: {config.num_requests or 'All'}")
    print(f"  QPS: {config.qps or 'From trace'}")
    print(f"  Output Directory: {config.output_dir}")
    print()

    # Run benchmark
    benchmark = VLLMBenchmark(config)
    try:
        await benchmark.run_benchmark()
        benchmark.save_results()
    except Exception as e:
        print(f"Benchmark failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
