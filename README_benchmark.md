# Vidur vs vLLM Benchmark Suite

This benchmark suite allows you to reproduce the comparison results between Vidur simulator and real vLLM engine, validating the claim in the Vidur paper that the simulation error is less than 9%.

## Overview

The benchmark suite consists of four main components:

1. **`benchmark_vllm.py`** - Runs real vLLM engine and collects performance metrics
2. **`benchmark_vidur.py`** - Runs Vidur simulation with equivalent configuration
3. **`compare_results.py`** - Compares results and calculates errors
4. **`run_benchmark.py`** - Automated pipeline to run all benchmarks

## Prerequisites

### Software Requirements

1. **Python 3.8+** with the following packages:
   ```bash
   pip install pandas numpy matplotlib seaborn
   ```

2. **vLLM** (for real engine benchmark):
   ```bash
   pip install vllm
   ```

3. **Vidur** (already available in this repository):
   ```bash
   pip install -e .
   ```

### Hardware Requirements

- **GPU**: NVIDIA GPU with sufficient memory (A100, H100, or A40 recommended)
- **Memory**: At least 40GB GPU memory for Llama-3-8B
- **Storage**: ~10GB for model weights and results

### Data Requirements

The benchmark uses trace files from `data/processed_traces/`. The default trace file is `splitwise_conv.csv` which contains:
- Request arrival times
- Number of prefill tokens per request
- Number of decode tokens per request

## Quick Start

### Option 1: Automated Pipeline (Recommended)

Run the complete benchmark pipeline with default settings:

```bash
python run_benchmark.py
```

This will:
1. Run vLLM benchmark with 500 requests
2. Run Vidur simulation with the same configuration
3. Compare results and generate error analysis
4. Create visualizations and reports

### Option 2: Manual Step-by-Step

1. **Run vLLM benchmark:**
   ```bash
   python benchmark_vllm.py --num-requests 500
   ```

2. **Run Vidur simulation:**
   ```bash
   python benchmark_vidur.py --num-requests 500
   ```

3. **Compare results:**
   ```bash
   python compare_results.py
   ```

## Configuration Options

### Model and Hardware Configuration

- `--model-name`: Model to benchmark (default: `meta-llama/Meta-Llama-3-8B`)
- `--tensor-parallel-size`: Tensor parallelism degree (default: 1)
- `--device`: Device type for Vidur (default: `a100`)
- `--max-model-len`: Maximum sequence length (default: 16384)
- `--block-size`: Memory block size (default: 16)

### Workload Configuration

- `--trace-file`: Path to request trace file (default: `./data/processed_traces/splitwise_conv.csv`)
- `--num-requests`: Number of requests to process (default: 500)
- `--qps`: Fixed queries per second (default: use trace timing)

### Output Configuration

- `--output-dir`: Directory for benchmark results (default: `./benchmark_output`)
- `--comparison-dir`: Directory for comparison results (default: `./comparison_output`)

## Example Configurations

### Small Scale Test (Fast)
```bash
python run_benchmark.py --num-requests 100 --max-model-len 4096
```

### Full Scale Test (Paper Reproduction)
```bash
python run_benchmark.py --num-requests 2000 --max-model-len 16384
```

### Different Model
```bash
python run_benchmark.py --model-name meta-llama/Llama-2-7b-hf --device a100
```

### Fixed QPS Test
```bash
python run_benchmark.py --qps 5.0 --num-requests 1000
```

## Understanding Results

### Key Metrics Compared

1. **TTFT (Time to First Token)**: Latency to generate the first token
2. **TPOT (Time per Output Token)**: Average time per subsequent token
3. **E2E Latency**: End-to-end request processing time
4. **Throughput**: Requests processed per second

### Output Files

The benchmark generates several output files:

#### Benchmark Results
- `vllm_summary_<timestamp>.json`: vLLM benchmark summary
- `vidur_summary_<timestamp>.json`: Vidur simulation summary
- `vllm_results_<timestamp>.csv`: Detailed vLLM results per request
- Vidur simulation outputs in `simulator_output/` directory

#### Comparison Results
- `detailed_comparison.csv`: Metric-by-metric comparison
- `comparison_summary.json`: Overall error statistics
- `comparison_plots.png`: Visualization of results

### Interpreting Error Results

The comparison script calculates:
- **Absolute Error**: `|Vidur_value - vLLM_value|`
- **Relative Error**: `(Absolute_Error / vLLM_value) × 100%`

Success criteria:
- ✅ **Mean relative error ≤ 9%**: Reproduces paper claim
- ✅ **Individual metric errors ≤ 9%**: High fidelity simulation
- ❌ **Mean relative error > 9%**: May indicate configuration issues

## Troubleshooting

### Common Issues

1. **vLLM Installation Issues**:
   ```bash
   pip install --upgrade vllm
   # or for specific CUDA version:
   pip install vllm --extra-index-url https://download.pytorch.org/whl/cu118
   ```

2. **GPU Memory Issues**:
   - Reduce `--max-model-len` or `--max-num-seqs`
   - Use smaller model like `meta-llama/Llama-2-7b-hf`
   - Increase `--tensor-parallel-size` if multiple GPUs available

3. **Missing Profiling Data**:
   - Ensure model and device combination exists in `data/profiling/`
   - Follow [profiling guide](docs/profiling.md) to add new models

4. **Trace File Issues**:
   - Verify trace file exists and has correct format
   - Use different trace file with `--trace-file`

### Debug Mode

Run individual components with verbose output:

```bash
# Debug vLLM benchmark
python benchmark_vllm.py --num-requests 10 --output-dir ./debug_output

# Debug Vidur simulation
python benchmark_vidur.py --num-requests 10 --output-dir ./debug_output

# Debug comparison
python compare_results.py --vllm-results-dir ./debug_output --vidur-results-dir ./debug_output
```

## Expected Results

Based on the Vidur paper, you should expect:
- Mean relative error across all metrics: **< 9%**
- TTFT error: **< 10%**
- TPOT error: **< 5%**
- Throughput error: **< 8%**

Typical runtime:
- vLLM benchmark (500 requests): 5-15 minutes
- Vidur simulation (500 requests): 1-3 minutes
- Results comparison: < 1 minute

## Contributing

To add support for new models or configurations:
1. Add model config to `data/model_configs/`
2. Generate profiling data following [profiling guide](docs/profiling.md)
3. Test with benchmark suite
4. Submit PR with results

## Citation

If you use this benchmark suite in your research, please cite the Vidur paper:

```bibtex
@article{agrawal2024vidur,
  title={Vidur: A Large-Scale Simulation Framework For LLM Inference},
  author={Agrawal, Amey and Kedia, Nitin and Mohan, Jayashree and Panwar, Ashish and Kwatra, Nipun and Gulavani, Bhargav S and Ramjee, Ramachandran and Tumanov, Alexey},
  journal={Proceedings of The Seventh Annual Conference on Machine Learning and Systems, 2024, Santa Clara},
  year={2024}
}
```
