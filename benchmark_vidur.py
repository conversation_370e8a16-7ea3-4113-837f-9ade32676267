#!/usr/bin/env python3
"""
Vidur Benchmark Script for vLLM Comparison

This script runs Vidur simulator with specified configurations and collects
performance metrics for comparison with real vLLM engine.
"""

import argparse
import json
import os
import time
from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Optional
import pandas as pd
import numpy as np
import tempfile
import subprocess
import sys

# Add vidur to path
sys.path.insert(0, '.')

from vidur.config import SimulationConfig
from vidur.simulator import Simulator
from vidur.utils.random import set_seeds


@dataclass
class VidurBenchmarkConfig:
    """Configuration for Vidur benchmark"""
    model_name: str = "meta-llama/Meta-Llama-3-8B"  # Keep HuggingFace format for Vidur
    device: str = "a100"
    tensor_parallel_size: int = 1
    num_pipeline_stages: int = 1
    max_model_len: int = 4096
    block_size: int = 16
    batch_size_cap: int = 64
    max_tokens_in_batch: int = 2048
    trace_file: str = "./data/processed_traces/splitwise_conv.csv"
    output_dir: str = "./benchmark_output"
    num_requests: Optional[int] = None
    qps: Optional[float] = None
    seed: int = 42


class VidurBenchmark:
    """Vidur benchmark runner"""
    
    def __init__(self, config: VidurBenchmarkConfig):
        self.config = config
        self.results: Dict[str, Any] = {}
        
        # Create output directory
        os.makedirs(config.output_dir, exist_ok=True)
        
    def create_vidur_config(self) -> SimulationConfig:
        """Create Vidur simulation configuration"""

        # Determine request generator type and parameters
        if self.config.qps:
            # Use synthetic requests with Poisson arrival
            request_generator_args = [
                "--request_generator_config_type", "synthetic",
                "--synthetic_request_generator_config_num_requests", str(self.config.num_requests or 1000),
                "--length_generator_config_type", "trace",
                "--trace_request_length_generator_config_trace_file", self.config.trace_file,
                "--trace_request_length_generator_config_max_tokens", str(self.config.max_model_len),
                "--interval_generator_config_type", "poisson",
                "--poisson_request_interval_generator_config_qps", str(self.config.qps),
            ]
        else:
            # Use trace replay
            request_generator_args = [
                "--request_generator_config_type", "trace_replay",
                "--trace_request_generator_config_trace_file", self.config.trace_file,
                "--trace_request_generator_config_max_tokens", str(self.config.max_model_len),
            ]

        if self.config.num_requests:
            request_generator_args.extend([
                "--trace_request_generator_config_num_requests", str(self.config.num_requests)
            ])

        # Build command line arguments for Vidur
        args = [
            # Replica configuration
            "--replica_config_device", self.config.device,
            "--replica_config_model_name", self.config.model_name,
            "--replica_config_tensor_parallel_size", str(self.config.tensor_parallel_size),
            "--replica_config_num_pipeline_stages", str(self.config.num_pipeline_stages),

            # Cluster configuration
            "--cluster_config_num_replicas", "1",

            # Scheduler configuration (vLLM)
            "--replica_scheduler_config_type", "vllm",
            "--vllm_scheduler_config_batch_size_cap", str(self.config.batch_size_cap),
            "--vllm_scheduler_config_max_tokens_in_batch", str(self.config.max_tokens_in_batch),
            "--vllm_scheduler_config_block_size", str(self.config.block_size),

            # Output configuration
            "--output_dir", self.config.output_dir,
            "--metrics_config_write_json_trace", "true",
            "--metrics_config_enable_chrome_trace", "false",

            # Random seed
            "--seed", str(self.config.seed),
        ] + request_generator_args

        # Temporarily replace sys.argv to pass arguments to create_from_cli_args
        import sys
        original_argv = sys.argv
        try:
            sys.argv = ["benchmark_vidur.py"] + args
            return SimulationConfig.create_from_cli_args()
        finally:
            sys.argv = original_argv
        
    def run_benchmark(self) -> None:
        """Run Vidur simulation benchmark"""
        print("Starting Vidur simulation benchmark...")
        
        # Create simulation configuration
        sim_config = self.create_vidur_config()
        
        print(f"Vidur configuration:")
        print(f"  Model: {sim_config.replica_config.model_name}")
        print(f"  Device: {sim_config.replica_config.device}")
        print(f"  Tensor Parallel Size: {sim_config.replica_config.tensor_parallel_size}")
        print(f"  Pipeline Stages: {sim_config.replica_config.num_pipeline_stages}")
        print(f"  Batch Size Cap: {sim_config.replica_scheduler_config.batch_size_cap}")
        print(f"  Max Tokens in Batch: {sim_config.replica_scheduler_config.max_tokens_in_batch}")
        print(f"  Block Size: {sim_config.replica_scheduler_config.block_size}")
        print()
        
        # Set random seed
        set_seeds(sim_config.seed)
        
        # Run simulation
        simulator = Simulator(sim_config)
        start_time = time.time()
        simulator.run()
        end_time = time.time()
        
        print(f"Vidur simulation completed in {end_time - start_time:.2f} seconds")
        
        # Extract results from simulation output
        self.extract_results(sim_config)
        
    def extract_results(self, sim_config: SimulationConfig) -> None:
        """Extract results from Vidur simulation output"""
        output_dir = sim_config.output_dir
        
        # Find the latest simulation output directory
        sim_dirs = [d for d in os.listdir(output_dir) if os.path.isdir(os.path.join(output_dir, d))]
        if not sim_dirs:
            raise RuntimeError("No simulation output directory found")
            
        latest_sim_dir = max(sim_dirs)
        sim_output_path = os.path.join(output_dir, latest_sim_dir)
        
        print(f"Extracting results from {sim_output_path}")
        
        # Load metrics files
        try:
            # Load request metrics
            request_metrics_file = os.path.join(sim_output_path, "request_metrics.csv")
            if os.path.exists(request_metrics_file):
                request_df = pd.read_csv(request_metrics_file)
            else:
                raise FileNotFoundError(f"Request metrics file not found: {request_metrics_file}")
                
            # Load TTFT metrics
            ttft_file = os.path.join(sim_output_path, "time_to_first_token.csv")
            if os.path.exists(ttft_file):
                ttft_df = pd.read_csv(ttft_file)
            else:
                print("Warning: TTFT file not found")
                ttft_df = pd.DataFrame()
                
            # Load TPOT metrics  
            tpot_file = os.path.join(sim_output_path, "time_per_output_token.csv")
            if os.path.exists(tpot_file):
                tpot_df = pd.read_csv(tpot_file)
            else:
                print("Warning: TPOT file not found")
                tpot_df = pd.DataFrame()
                
        except Exception as e:
            print(f"Error loading metrics files: {e}")
            raise
            
        # Calculate summary statistics
        self.calculate_summary_stats(request_df, ttft_df, tpot_df, sim_output_path)
        
    def calculate_summary_stats(self, request_df: pd.DataFrame, ttft_df: pd.DataFrame, 
                              tpot_df: pd.DataFrame, sim_output_path: str) -> None:
        """Calculate and save summary statistics"""
        
        # Extract key metrics from request_df
        if 'request_e2e_time' in request_df.columns:
            e2e_latencies = request_df['request_e2e_time'].dropna()
        else:
            print("Warning: request_e2e_time not found in request metrics")
            e2e_latencies = pd.Series()
            
        # Extract TTFT values
        if not ttft_df.empty and 'time' in ttft_df.columns:
            ttfts = ttft_df['time'].dropna()
        else:
            ttfts = pd.Series()
            
        # Extract TPOT values
        if not tpot_df.empty and 'time' in tpot_df.columns:
            tpots = tpot_df['time'].dropna()
        else:
            tpots = pd.Series()
            
        # Calculate throughput
        if not request_df.empty:
            total_requests = len(request_df)
            if 'request_completion_time' in request_df.columns:
                completion_times = request_df['request_completion_time'].dropna()
                if not completion_times.empty:
                    simulation_duration = completion_times.max() - completion_times.min()
                    throughput = total_requests / simulation_duration if simulation_duration > 0 else 0
                else:
                    throughput = 0
            else:
                throughput = 0
        else:
            total_requests = 0
            throughput = 0
            
        # Create summary
        summary = {
            'config': asdict(self.config),
            'total_requests': total_requests,
            'throughput_qps': throughput,
            'ttft_stats': self._calculate_stats(ttfts),
            'tpot_stats': self._calculate_stats(tpots),
            'e2e_latency_stats': self._calculate_stats(e2e_latencies),
        }
        
        # Save summary
        timestamp = int(time.time())
        summary_file = os.path.join(self.config.output_dir, f"vidur_summary_{timestamp}.json")
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2)
            
        print(f"Summary statistics saved to {summary_file}")
        
        # Print key metrics
        print("\n=== Vidur Simulation Results ===")
        print(f"Total requests: {summary['total_requests']}")
        print(f"Throughput: {summary['throughput_qps']:.2f} QPS")
        if summary['ttft_stats']['mean'] is not None:
            print(f"TTFT - Mean: {summary['ttft_stats']['mean']:.3f}s, P95: {summary['ttft_stats']['p95']:.3f}s")
        if summary['tpot_stats']['mean'] is not None:
            print(f"TPOT - Mean: {summary['tpot_stats']['mean']:.3f}s, P95: {summary['tpot_stats']['p95']:.3f}s")
        if summary['e2e_latency_stats']['mean'] is not None:
            print(f"E2E Latency - Mean: {summary['e2e_latency_stats']['mean']:.3f}s, P95: {summary['e2e_latency_stats']['p95']:.3f}s")
            
        self.results = summary
        
    def _calculate_stats(self, data: pd.Series) -> Dict[str, Optional[float]]:
        """Calculate statistics for a data series"""
        if data.empty:
            return {
                'mean': None, 'median': None, 'p95': None, 'p99': None, 'std': None
            }
            
        return {
            'mean': float(data.mean()),
            'median': float(data.median()),
            'p95': float(data.quantile(0.95)),
            'p99': float(data.quantile(0.99)),
            'std': float(data.std()),
        }


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Vidur Benchmark for vLLM Comparison")

    # Model and hardware configuration
    parser.add_argument("--model-name", type=str, default="meta-llama/Meta-Llama-3-8B",
                        help="Model name (default: meta-llama/Meta-Llama-3-8B)")
    parser.add_argument("--device", type=str, default="a100",
                        help="Device type (default: a100)")
    parser.add_argument("--tensor-parallel-size", type=int, default=1,
                        help="Tensor parallel size (default: 1)")
    parser.add_argument("--num-pipeline-stages", type=int, default=1,
                        help="Number of pipeline stages (default: 1)")
    parser.add_argument("--max-model-len", type=int, default=4096,
                        help="Maximum model length (default: 4096)")
    parser.add_argument("--block-size", type=int, default=16,
                        help="Block size (default: 16)")
    parser.add_argument("--batch-size-cap", type=int, default=64,
                        help="Batch size cap (default: 64)")
    parser.add_argument("--max-tokens-in-batch", type=int, default=2048,
                        help="Maximum tokens in batch (default: 2048)")

    # Workload configuration
    parser.add_argument("--trace-file", type=str, default="./data/processed_traces/splitwise_conv.csv",
                        help="Path to trace file (default: ./data/processed_traces/splitwise_conv.csv)")
    parser.add_argument("--num-requests", type=int, default=None,
                        help="Number of requests to process (default: all)")
    parser.add_argument("--qps", type=float, default=None,
                        help="Queries per second (default: use trace timing)")

    # Output configuration
    parser.add_argument("--output-dir", type=str, default="./benchmark_output",
                        help="Output directory (default: ./benchmark_output)")
    parser.add_argument("--seed", type=int, default=42,
                        help="Random seed (default: 42)")

    args = parser.parse_args()

    # Create configuration
    config = VidurBenchmarkConfig(
        model_name=args.model_name,
        device=args.device,
        tensor_parallel_size=args.tensor_parallel_size,
        num_pipeline_stages=args.num_pipeline_stages,
        max_model_len=args.max_model_len,
        block_size=args.block_size,
        batch_size_cap=args.batch_size_cap,
        max_tokens_in_batch=args.max_tokens_in_batch,
        trace_file=args.trace_file,
        output_dir=args.output_dir,
        num_requests=args.num_requests,
        qps=args.qps,
        seed=args.seed,
    )

    print("Vidur Benchmark Configuration:")
    print(f"  Model: {config.model_name}")
    print(f"  Device: {config.device}")
    print(f"  Tensor Parallel Size: {config.tensor_parallel_size}")
    print(f"  Pipeline Stages: {config.num_pipeline_stages}")
    print(f"  Max Model Length: {config.max_model_len}")
    print(f"  Block Size: {config.block_size}")
    print(f"  Batch Size Cap: {config.batch_size_cap}")
    print(f"  Max Tokens in Batch: {config.max_tokens_in_batch}")
    print(f"  Trace File: {config.trace_file}")
    print(f"  Number of Requests: {config.num_requests or 'All'}")
    print(f"  QPS: {config.qps or 'From trace'}")
    print(f"  Output Directory: {config.output_dir}")
    print(f"  Seed: {config.seed}")
    print()

    # Run benchmark
    benchmark = VidurBenchmark(config)
    try:
        benchmark.run_benchmark()
    except Exception as e:
        print(f"Benchmark failed: {e}")
        raise


if __name__ == "__main__":
    main()
