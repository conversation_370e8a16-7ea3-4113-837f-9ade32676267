#!/usr/bin/env python3
"""
Test script to validate the benchmark setup before running full comparison
"""

import os
import sys
import subprocess
import json
import pandas as pd


def check_prerequisites():
    """Check if all prerequisites are installed"""
    print("Checking prerequisites...")
    
    # Check Python packages
    required_packages = ['pandas', 'numpy', 'matplotlib', 'seaborn']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} is installed")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} is missing")
    
    # Check vLLM
    try:
        import vllm
        print("✅ vLLM is installed")
    except ImportError:
        missing_packages.append('vllm')
        print("❌ vLLM is missing")
    
    # Check Vidur components
    try:
        from vidur.config import SimulationConfig
        from vidur.simulator import Simulator
        print("✅ Vidur is available")
    except ImportError:
        print("❌ Vidur is not properly installed")
        missing_packages.append('vidur')
    
    if missing_packages:
        print(f"\nMissing packages: {missing_packages}")
        print("Please install missing packages before running benchmarks")
        return False
    
    print("✅ All prerequisites are installed")
    return True


def check_data_files():
    """Check if required data files exist"""
    print("\nChecking data files...")
    
    # Check trace file
    trace_file = "./data/processed_traces/splitwise_conv.csv"
    if os.path.exists(trace_file):
        print(f"✅ Trace file exists: {trace_file}")
        
        # Check trace file format
        try:
            df = pd.read_csv(trace_file)
            required_columns = ['arrived_at', 'num_prefill_tokens', 'num_decode_tokens']
            if all(col in df.columns for col in required_columns):
                print(f"✅ Trace file has correct format ({len(df)} requests)")
            else:
                print(f"❌ Trace file missing columns: {set(required_columns) - set(df.columns)}")
                return False
        except Exception as e:
            print(f"❌ Error reading trace file: {e}")
            return False
    else:
        print(f"❌ Trace file not found: {trace_file}")
        return False
    
    # Check model config
    model_config = "./data/model_configs/meta-llama/Meta-Llama-3-8B.yml"
    if os.path.exists(model_config):
        print(f"✅ Model config exists: {model_config}")
    else:
        print(f"❌ Model config not found: {model_config}")
        return False
    
    # Check profiling data
    profiling_dir = "./data/profiling/compute/a100/meta-llama/Meta-Llama-3-8B"
    if os.path.exists(profiling_dir):
        attention_file = os.path.join(profiling_dir, "attention.csv")
        mlp_file = os.path.join(profiling_dir, "mlp.csv")
        
        if os.path.exists(attention_file) and os.path.exists(mlp_file):
            print(f"✅ Profiling data exists: {profiling_dir}")
        else:
            print(f"❌ Missing profiling files in: {profiling_dir}")
            return False
    else:
        print(f"❌ Profiling directory not found: {profiling_dir}")
        return False
    
    return True


def test_vidur_simulation():
    """Test Vidur simulation with minimal configuration"""
    print("\nTesting Vidur simulation...")
    
    try:
        cmd = [
            sys.executable, "benchmark_vidur.py",
            "--num-requests", "5",
            "--max-model-len", "1024",
            "--output-dir", "./test_output"
        ]
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Vidur simulation test passed")
            return True
        else:
            print("❌ Vidur simulation test failed")
            print("STDERR:", result.stderr[-500:])
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Vidur simulation test timed out")
        return False
    except Exception as e:
        print(f"❌ Vidur simulation test error: {e}")
        return False


def test_vllm_availability():
    """Test if vLLM can be imported and basic functionality works"""
    print("\nTesting vLLM availability...")
    
    try:
        # Test basic vLLM import and functionality
        test_code = '''
import sys
try:
    from vllm import LLM, SamplingParams
    from vllm.engine.arg_utils import AsyncEngineArgs
    from vllm.engine.async_llm_engine import AsyncLLMEngine
    print("✅ vLLM imports successful")
    sys.exit(0)
except Exception as e:
    print(f"❌ vLLM import failed: {e}")
    sys.exit(1)
'''
        
        result = subprocess.run([sys.executable, "-c", test_code], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ vLLM is functional")
            return True
        else:
            print("❌ vLLM test failed")
            print("STDERR:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ vLLM test timed out")
        return False
    except Exception as e:
        print(f"❌ vLLM test error: {e}")
        return False


def test_gpu_availability():
    """Test GPU availability"""
    print("\nTesting GPU availability...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            
            print(f"✅ GPU available: {gpu_name}")
            print(f"✅ GPU memory: {gpu_memory:.1f} GB")
            print(f"✅ GPU count: {gpu_count}")
            
            if gpu_memory < 20:
                print("⚠️  Warning: GPU memory < 20GB, may not be sufficient for larger models")
            
            return True
        else:
            print("❌ No GPU available")
            return False
            
    except ImportError:
        print("❌ PyTorch not available")
        return False
    except Exception as e:
        print(f"❌ GPU test error: {e}")
        return False


def run_quick_test():
    """Run a quick end-to-end test"""
    print("\nRunning quick end-to-end test...")
    
    try:
        # Create test output directory
        os.makedirs("./test_output", exist_ok=True)
        
        # Run minimal benchmark
        cmd = [
            sys.executable, "run_benchmark.py",
            "--num-requests", "3",
            "--max-model-len", "512",
            "--output-dir", "./test_output",
            "--comparison-dir", "./test_comparison",
            "--no-plots"
        ]
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ Quick end-to-end test passed")
            
            # Check if output files were created
            if os.path.exists("./test_comparison/comparison_summary.json"):
                with open("./test_comparison/comparison_summary.json", 'r') as f:
                    summary = json.load(f)
                    mean_error = summary['overall_statistics']['mean_relative_error_percent']
                    print(f"✅ Mean relative error: {mean_error:.2f}%")
            
            return True
        else:
            print("❌ Quick end-to-end test failed")
            print("STDERR:", result.stderr[-1000:])
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Quick test timed out")
        return False
    except Exception as e:
        print(f"❌ Quick test error: {e}")
        return False


def main():
    """Main test function"""
    print("="*60)
    print("VIDUR vs vLLM BENCHMARK SETUP TEST")
    print("="*60)
    
    tests = [
        ("Prerequisites", check_prerequisites),
        ("Data Files", check_data_files),
        ("GPU Availability", test_gpu_availability),
        ("vLLM Functionality", test_vllm_availability),
        ("Vidur Simulation", test_vidur_simulation),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed_tests += 1
            else:
                print(f"❌ {test_name} test failed")
        except Exception as e:
            print(f"❌ {test_name} test error: {e}")
    
    print(f"\n{'='*60}")
    print(f"TEST SUMMARY: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("✅ All tests passed! Ready to run benchmarks.")
        
        # Ask if user wants to run quick test
        try:
            response = input("\nRun quick end-to-end test? (y/n): ").lower().strip()
            if response == 'y':
                if run_quick_test():
                    print("\n🎉 Setup validation completed successfully!")
                    print("You can now run the full benchmark with: python run_benchmark.py")
                else:
                    print("\n⚠️  Quick test failed, but basic setup seems OK")
        except KeyboardInterrupt:
            print("\nSkipping quick test")
            
        return 0
    else:
        print("❌ Some tests failed. Please fix issues before running benchmarks.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
