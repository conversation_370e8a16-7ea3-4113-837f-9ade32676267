#!/usr/bin/env python3
"""
Automated Benchmark Runner for Vidur vs vLLM Comparison

This script automates the entire benchmark process:
1. Runs vLLM benchmark
2. Runs Vidur simulation
3. Compares results and generates report
"""

import argparse
import asyncio
import os
import subprocess
import sys
import time
from typing import Dict, Any, Optional


class BenchmarkRunner:
    """Automated benchmark runner"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.output_dir = config.get('output_dir', './benchmark_output')
        self.comparison_dir = config.get('comparison_dir', './comparison_output')
        
        # Create directories
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.comparison_dir, exist_ok=True)
        
    def run_vllm_benchmark(self) -> bool:
        """Run vLLM benchmark"""
        print("="*60)
        print("RUNNING vLLM BENCHMARK")
        print("="*60)
        
        cmd = [
            sys.executable, "benchmark_vllm.py",
            "--model-name", self.config['model_name'],
            "--tensor-parallel-size", str(self.config['tensor_parallel_size']),
            "--max-model-len", str(self.config['max_model_len']),
            "--block-size", str(self.config['block_size']),
            "--max-num-seqs", str(self.config['max_num_seqs']),
            "--trace-file", self.config['trace_file'],
            "--output-dir", self.output_dir,
        ]
        
        if self.config.get('num_requests'):
            cmd.extend(["--num-requests", str(self.config['num_requests'])])
            
        if self.config.get('qps'):
            cmd.extend(["--qps", str(self.config['qps'])])
            
        print(f"Running command: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print("vLLM benchmark completed successfully")
            print("STDOUT:", result.stdout[-1000:])  # Last 1000 chars
            return True
        except subprocess.CalledProcessError as e:
            print(f"vLLM benchmark failed: {e}")
            print("STDOUT:", e.stdout)
            print("STDERR:", e.stderr)
            return False
            
    def run_vidur_benchmark(self) -> bool:
        """Run Vidur simulation benchmark"""
        print("="*60)
        print("RUNNING VIDUR SIMULATION")
        print("="*60)
        
        cmd = [
            sys.executable, "benchmark_vidur.py",
            "--model-name", self.config['model_name'],
            "--device", self.config.get('device', 'a100'),
            "--tensor-parallel-size", str(self.config['tensor_parallel_size']),
            "--num-pipeline-stages", str(self.config.get('num_pipeline_stages', 1)),
            "--max-model-len", str(self.config['max_model_len']),
            "--block-size", str(self.config['block_size']),
            "--batch-size-cap", str(self.config.get('batch_size_cap', 128)),
            "--max-tokens-in-batch", str(self.config.get('max_tokens_in_batch', 4096)),
            "--trace-file", self.config['trace_file'],
            "--output-dir", self.output_dir,
            "--seed", str(self.config.get('seed', 42)),
        ]
        
        if self.config.get('num_requests'):
            cmd.extend(["--num-requests", str(self.config['num_requests'])])
            
        if self.config.get('qps'):
            cmd.extend(["--qps", str(self.config['qps'])])
            
        print(f"Running command: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print("Vidur simulation completed successfully")
            print("STDOUT:", result.stdout[-1000:])  # Last 1000 chars
            return True
        except subprocess.CalledProcessError as e:
            print(f"Vidur simulation failed: {e}")
            print("STDOUT:", e.stdout)
            print("STDERR:", e.stderr)
            return False
            
    def compare_results(self) -> bool:
        """Compare results and generate report"""
        print("="*60)
        print("COMPARING RESULTS")
        print("="*60)
        
        cmd = [
            sys.executable, "compare_results.py",
            "--vllm-results-dir", self.output_dir,
            "--vidur-results-dir", self.output_dir,
            "--output-dir", self.comparison_dir,
        ]
        
        if self.config.get('no_plots', False):
            cmd.append("--no-plots")
            
        print(f"Running command: {' '.join(cmd)}")
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            print("Results comparison completed successfully")
            print("STDOUT:", result.stdout[-1000:])  # Last 1000 chars
            return True
        except subprocess.CalledProcessError as e:
            print(f"Results comparison failed: {e}")
            print("STDOUT:", e.stdout)
            print("STDERR:", e.stderr)
            return False
            
    def run_full_benchmark(self) -> bool:
        """Run the complete benchmark pipeline"""
        print("Starting full benchmark pipeline...")
        print(f"Configuration: {self.config}")
        print()
        
        start_time = time.time()
        
        # Step 1: Run vLLM benchmark
        if not self.run_vllm_benchmark():
            print("❌ vLLM benchmark failed")
            return False
            
        print("✅ vLLM benchmark completed")
        print()
        
        # Step 2: Run Vidur simulation
        if not self.run_vidur_benchmark():
            print("❌ Vidur simulation failed")
            return False
            
        print("✅ Vidur simulation completed")
        print()
        
        # Step 3: Compare results
        if not self.compare_results():
            print("❌ Results comparison failed")
            return False
            
        print("✅ Results comparison completed")
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print()
        print("="*60)
        print("BENCHMARK PIPELINE COMPLETED SUCCESSFULLY")
        print("="*60)
        print(f"Total time: {total_time:.2f} seconds")
        print(f"Results saved to: {self.output_dir}")
        print(f"Comparison report saved to: {self.comparison_dir}")
        
        return True


def create_default_config() -> Dict[str, Any]:
    """Create default benchmark configuration"""
    return {
        'model_name': 'meta-llama/Meta-Llama-3-8B',
        'device': 'a100',
        'tensor_parallel_size': 1,
        'num_pipeline_stages': 1,
        'max_model_len': 16384,
        'block_size': 16,
        'max_num_seqs': 128,
        'batch_size_cap': 128,
        'max_tokens_in_batch': 4096,
        'trace_file': './data/processed_traces/splitwise_conv.csv',
        'output_dir': './benchmark_output',
        'comparison_dir': './comparison_output',
        'num_requests': 500,  # Limit for faster testing
        'qps': None,  # Use trace timing
        'seed': 42,
        'no_plots': False,
    }


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Automated Vidur vs vLLM Benchmark")
    
    # Model and hardware configuration
    parser.add_argument("--model-name", type=str, default="meta-llama/Meta-Llama-3-8B",
                        help="Model name")
    parser.add_argument("--device", type=str, default="a100",
                        help="Device type for Vidur")
    parser.add_argument("--tensor-parallel-size", type=int, default=1,
                        help="Tensor parallel size")
    parser.add_argument("--num-pipeline-stages", type=int, default=1,
                        help="Number of pipeline stages")
    parser.add_argument("--max-model-len", type=int, default=16384,
                        help="Maximum model length")
    parser.add_argument("--block-size", type=int, default=16,
                        help="Block size")
    parser.add_argument("--max-num-seqs", type=int, default=128,
                        help="Maximum number of sequences")
    parser.add_argument("--batch-size-cap", type=int, default=128,
                        help="Batch size cap")
    parser.add_argument("--max-tokens-in-batch", type=int, default=4096,
                        help="Maximum tokens in batch")
    
    # Workload configuration
    parser.add_argument("--trace-file", type=str, default="./data/processed_traces/splitwise_conv.csv",
                        help="Path to trace file")
    parser.add_argument("--num-requests", type=int, default=500,
                        help="Number of requests to process")
    parser.add_argument("--qps", type=float, default=None,
                        help="Queries per second")
    
    # Output configuration
    parser.add_argument("--output-dir", type=str, default="./benchmark_output",
                        help="Output directory")
    parser.add_argument("--comparison-dir", type=str, default="./comparison_output",
                        help="Comparison output directory")
    parser.add_argument("--seed", type=int, default=42,
                        help="Random seed")
    parser.add_argument("--no-plots", action="store_true",
                        help="Skip generating plots")
    
    # Pipeline control
    parser.add_argument("--skip-vllm", action="store_true",
                        help="Skip vLLM benchmark")
    parser.add_argument("--skip-vidur", action="store_true",
                        help="Skip Vidur simulation")
    parser.add_argument("--skip-comparison", action="store_true",
                        help="Skip results comparison")
    
    args = parser.parse_args()
    
    # Create configuration
    config = {
        'model_name': args.model_name,
        'device': args.device,
        'tensor_parallel_size': args.tensor_parallel_size,
        'num_pipeline_stages': args.num_pipeline_stages,
        'max_model_len': args.max_model_len,
        'block_size': args.block_size,
        'max_num_seqs': args.max_num_seqs,
        'batch_size_cap': args.batch_size_cap,
        'max_tokens_in_batch': args.max_tokens_in_batch,
        'trace_file': args.trace_file,
        'output_dir': args.output_dir,
        'comparison_dir': args.comparison_dir,
        'num_requests': args.num_requests,
        'qps': args.qps,
        'seed': args.seed,
        'no_plots': args.no_plots,
    }
    
    print("Automated Benchmark Configuration:")
    for key, value in config.items():
        print(f"  {key}: {value}")
    print()
    
    # Run benchmark
    runner = BenchmarkRunner(config)
    
    try:
        success = True
        
        if not args.skip_vllm:
            success &= runner.run_vllm_benchmark()
            
        if success and not args.skip_vidur:
            success &= runner.run_vidur_benchmark()
            
        if success and not args.skip_comparison:
            success &= runner.compare_results()
            
        if success:
            print("\n🎉 All benchmarks completed successfully!")
            return 0
        else:
            print("\n💥 Benchmark pipeline failed")
            return 1
            
    except KeyboardInterrupt:
        print("\n⏹️  Benchmark interrupted by user")
        return 1
    except Exception as e:
        print(f"\n💥 Benchmark failed with error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
